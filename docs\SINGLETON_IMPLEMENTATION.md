# Singleton Pattern Implementation - PlanBook AI

## Tổng quan

Hệ thống PlanBook AI đã được cập nhật để sử dụng Singleton pattern với lazy initialization cho tất cả các services. Điều này đảm bảo:

- ✅ Mỗi service chỉ được khởi tạo **một lần duy nhất**
- ✅ Khởi tạo **lazy** - chỉ khi cần thiết lần đầu tiên
- ✅ **Thread-safe** - an toàn trong môi trường đa luồng
- ✅ **Memory efficient** - tiết kiệm bộ nhớ
- ✅ **Centralized management** - quản lý tập trung qua Service Registry

## Các Services đã được chuyển đổi

### 1. Synchronous Services
- **LLMService** - Xử lý LLM (Gemini, OpenRouter)
- **SimpleOCRService** - OCR với EasyOCR và Tesseract
- **DocxExportService** - Xuất giáo án ra DOCX
- **DocxUploadService** - Upload và xử lý file DOCX
- **ExamDocxService** - Tạo đề thi DOCX

### 2. Asynchronous Services
- **LessonPlanFrameworkService** - Quản lý framework giáo án
- **KafkaService** - Message queue với Kafka

## Cấu trúc Implementation

### Base Classes

```python
# app/core/singleton_base.py
class SingletonService:
    """Base class cho synchronous singleton services"""
    
class AsyncSingletonService:
    """Base class cho asynchronous singleton services"""
```

### Service Registry

```python
# app/core/service_registry.py
SERVICE_REGISTRY = {
    'llm_service': llm_service,
    'lesson_plan_framework_service': lesson_plan_framework_service,
    # ... other services
}
```

## Cách sử dụng

### 1. Import và sử dụng global instances

```python
from app.services.llm_service import llm_service
from app.services.simple_ocr_service import simple_ocr_service

# Sử dụng trực tiếp
result = await llm_service.generate_content("prompt")
text = await simple_ocr_service.extract_text_from_pdf(pdf_bytes, "file.pdf")
```

### 2. Sử dụng Service Registry

```python
from app.core.service_registry import get_service, get_llm_service

# Lấy service theo tên
llm = get_service('llm_service')

# Hoặc sử dụng factory function
llm = get_llm_service()
```

### 3. Khởi tạo và cleanup

```python
from app.core.service_registry import initialize_async_services, cleanup_all_services

# Khởi tạo tất cả async services
await initialize_async_services()

# Cleanup tất cả services
await cleanup_all_services()
```

## API Changes

### Startup/Shutdown Events

```python
# app/api/api.py
@app.on_event("startup")
async def startup_event():
    await initialize_async_services()

@app.on_event("shutdown") 
async def shutdown_event():
    await cleanup_all_services()
```

### Health Check Endpoint

```
GET /health/services
```

Trả về status của tất cả singleton services.

## Testing

### Unit Tests

```bash
# Chạy test cases
python -m pytest tests/test_singleton_services.py -v
```

### System Test

```bash
# Chạy comprehensive test
python scripts/test_singleton_system.py
```

### Test Cases bao gồm:

- ✅ **Singleton behavior** - Verify cùng instance
- ✅ **Thread safety** - Test đa luồng
- ✅ **Lazy initialization** - Chỉ init khi cần
- ✅ **Memory efficiency** - Kiểm tra memory usage
- ✅ **Service registry** - Test registry functions
- ✅ **Async services** - Test async init/cleanup

## Benefits

### 1. Memory Efficiency
- Giảm memory footprint đáng kể
- Tránh duplicate instances
- Efficient resource sharing

### 2. Performance
- Faster subsequent instantiations
- Reduced initialization overhead
- Better resource utilization

### 3. Consistency
- Single source of truth cho mỗi service
- Consistent state across application
- Easier debugging và monitoring

### 4. Maintainability
- Centralized service management
- Clear service lifecycle
- Easier testing và mocking

## Migration Notes

### Before (Old Pattern)
```python
# Mỗi lần tạo instance mới
llm_service = LLMService()
ocr_service = SimpleOCRService()
```

### After (Singleton Pattern)
```python
# Sử dụng global singleton instances
from app.services.llm_service import llm_service
from app.services.simple_ocr_service import simple_ocr_service

# Hoặc tạo mới (vẫn trả về cùng instance)
llm = LLMService()  # Same as llm_service
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Đảm bảo import từ đúng module
   - Check circular imports

2. **Initialization Errors**
   - Check async services được init đúng cách
   - Verify dependencies available

3. **Memory Issues**
   - Monitor service cleanup
   - Check for resource leaks

### Debug Commands

```python
# Check service status
from app.core.service_registry import get_service_status
status = get_service_status()
print(status)

# List all services
from app.core.service_registry import list_services
services = list_services()
print(services)
```

## Future Enhancements

- [ ] Service health monitoring
- [ ] Automatic service recovery
- [ ] Service dependency injection
- [ ] Configuration-based service management
- [ ] Service metrics và analytics

---

**Tác giả**: PlanBook AI Development Team  
**Ngày cập nhật**: 2025-01-27  
**Version**: 1.0
