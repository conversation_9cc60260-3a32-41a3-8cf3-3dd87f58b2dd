"""
Test cases để verify Singleton pattern hoạt động đúng
"""

import pytest
import asyncio
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import List

from app.core.singleton_base import SingletonMeta
from app.core.service_registry import (
    SERVICE_REGISTRY,
    get_service,
    list_services,
    get_service_status,
    initialize_async_services,
    cleanup_all_services
)

# Import services to test
from app.services.llm_service import LLMService, llm_service
from app.services.simple_ocr_service import SimpleOCRService, simple_ocr_service
from app.services.docx_export_service import DocxExportService, docx_export_service
from app.services.docx_upload_service import DocxUploadService, docx_upload_service
from app.services.exam_docx_service import ExamDocxService, exam_docx_service
from app.services.lesson_plan_framework_service import LessonPlanFrameworkService, lesson_plan_framework_service
from app.services.kafka_service import KafkaService, kafka_service


class TestSingletonPattern:
    """Test Singleton pattern implementation"""
    
    def test_singleton_instances_are_same(self):
        """Test that multiple instantiations return the same instance"""
        # Test LLMService
        service1 = LLMService()
        service2 = LLMService()
        assert service1 is service2, "LLMService instances should be the same"
        
        # Test SimpleOCRService
        ocr1 = SimpleOCRService()
        ocr2 = SimpleOCRService()
        assert ocr1 is ocr2, "SimpleOCRService instances should be the same"
        
        # Test DocxExportService
        export1 = DocxExportService()
        export2 = DocxExportService()
        assert export1 is export2, "DocxExportService instances should be the same"
    
    def test_global_instances_are_singletons(self):
        """Test that global instances are the same as new instantiations"""
        assert llm_service is LLMService(), "Global llm_service should be singleton"
        assert simple_ocr_service is SimpleOCRService(), "Global simple_ocr_service should be singleton"
        assert docx_export_service is DocxExportService(), "Global docx_export_service should be singleton"
        assert docx_upload_service is DocxUploadService(), "Global docx_upload_service should be singleton"
        assert exam_docx_service is ExamDocxService(), "Global exam_docx_service should be singleton"
    
    def test_thread_safety(self):
        """Test that Singleton pattern is thread-safe"""
        instances = []
        
        def create_instance():
            instances.append(LLMService())
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=create_instance)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All instances should be the same
        first_instance = instances[0]
        for instance in instances:
            assert instance is first_instance, "All instances should be the same in multi-threaded environment"
    
    def test_concurrent_initialization(self):
        """Test concurrent initialization doesn't create multiple instances"""
        # Reset singleton for testing
        if LLMService in SingletonMeta._instances:
            del SingletonMeta._instances[LLMService]
            SingletonMeta._initialized[LLMService] = False
        
        instances = []
        
        def create_and_store():
            instances.append(LLMService())
        
        # Use ThreadPoolExecutor for concurrent execution
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_and_store) for _ in range(10)]
            
            # Wait for all futures to complete
            for future in futures:
                future.result()
        
        # All instances should be the same
        first_instance = instances[0]
        for instance in instances:
            assert instance is first_instance, "Concurrent initialization should create only one instance"


class TestServiceRegistry:
    """Test Service Registry functionality"""
    
    def test_service_registry_contains_all_services(self):
        """Test that service registry contains all expected services"""
        expected_services = [
            'llm_service',
            'lesson_plan_framework_service',
            'kafka_service',
            'simple_ocr_service',
            'docx_export_service',
            'docx_upload_service',
            'exam_docx_service',
        ]
        
        registered_services = list_services()
        
        for service_name in expected_services:
            assert service_name in registered_services, f"Service {service_name} should be registered"
    
    def test_get_service_by_name(self):
        """Test getting services by name"""
        # Test getting existing service
        llm = get_service('llm_service')
        assert llm is llm_service, "Should return the correct service instance"
        
        # Test getting non-existent service
        with pytest.raises(KeyError):
            get_service('non_existent_service')
    
    def test_service_status(self):
        """Test service status reporting"""
        status = get_service_status()
        
        assert isinstance(status, dict), "Status should be a dictionary"
        assert len(status) > 0, "Should have at least one service"
        
        # Check that each service has required fields
        for service_name, service_info in status.items():
            assert 'name' in service_info, f"Service {service_name} should have name field"
            assert 'class' in service_info, f"Service {service_name} should have class field"
            assert 'initialized' in service_info, f"Service {service_name} should have initialized field"


class TestAsyncServices:
    """Test async service functionality"""
    
    @pytest.mark.asyncio
    async def test_async_service_initialization(self):
        """Test async service initialization"""
        try:
            await initialize_async_services()
            # Should not raise any exceptions
            assert True, "Async services should initialize without errors"
        except Exception as e:
            # Some services might fail in test environment (e.g., no MongoDB, Kafka)
            # This is expected and should not fail the test
            print(f"Expected error in test environment: {e}")
    
    @pytest.mark.asyncio
    async def test_service_cleanup(self):
        """Test service cleanup functionality"""
        try:
            await cleanup_all_services()
            # Should not raise any exceptions
            assert True, "Service cleanup should complete without errors"
        except Exception as e:
            # Some cleanup might fail in test environment
            print(f"Expected error during cleanup in test environment: {e}")


class TestLazyInitialization:
    """Test lazy initialization behavior"""
    
    def test_initialization_only_happens_once(self):
        """Test that initialization only happens once per service"""
        # Reset singleton for testing
        if SimpleOCRService in SingletonMeta._instances:
            del SingletonMeta._instances[SimpleOCRService]
            SingletonMeta._initialized[SimpleOCRService] = False
        
        # Create multiple instances
        service1 = SimpleOCRService()
        service2 = SimpleOCRService()
        service3 = SimpleOCRService()
        
        # All should be the same instance
        assert service1 is service2 is service3, "All instances should be the same"
        
        # Check that initialization flag is set
        assert SingletonMeta._initialized.get(SimpleOCRService, False), "Service should be marked as initialized"
    
    def test_service_has_required_methods(self):
        """Test that singleton services have required methods"""
        services_to_test = [
            (llm_service, 'LLMService'),
            (simple_ocr_service, 'SimpleOCRService'),
            (docx_export_service, 'DocxExportService'),
            (docx_upload_service, 'DocxUploadService'),
            (exam_docx_service, 'ExamDocxService'),
        ]
        
        for service, service_name in services_to_test:
            # Check that service has _initialize method
            assert hasattr(service, '_initialize'), f"{service_name} should have _initialize method"
            
            # Check that service has _cleanup method
            assert hasattr(service, '_cleanup'), f"{service_name} should have _cleanup method"


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
