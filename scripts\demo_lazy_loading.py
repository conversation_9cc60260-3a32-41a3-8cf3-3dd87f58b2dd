#!/usr/bin/env python3
"""
Demo script để chứng minh Lazy Loading hoạt động
"""

import sys
import os
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("🎯 DEMO: True Lazy Initialization")
print("=" * 50)

print("\n📚 Giải thích:")
print("- Lazy Loading = Chỉ tạo instance khi CẦN THIẾT lần đầu")
print("- Eager Loading = Tạo instance NGAY KHI IMPORT")
print()

# Demo 1: Import không tạo instance
print("1️⃣ IMPORT MODULE (không tạo instance)")
print("-" * 30)

start_time = time.time()
print("📦 import app.services.llm_service...")

# Import module - should be fast, no instance created
import app.services.llm_service as llm_module

import_time = time.time() - start_time
print(f"✅ Import hoàn thành trong {import_time:.4f} giây")
print("   → Chưa có instance nào được tạo!")

# Demo 2: Access service tạo instance
print("\n2️⃣ ACCESS SERVICE (tạo instance BÂY GIỜ)")
print("-" * 30)

print("🔍 Truy cập llm_service lần đầu...")
start_time = time.time()

# This should create the instance NOW
try:
    llm_service = llm_module.llm_service
    creation_time = time.time() - start_time
    
    print(f"✅ Instance được tạo trong {creation_time:.4f} giây")
    print(f"📋 Service type: {type(llm_service).__name__}")
    print("   → Instance được tạo KHI CẦN THIẾT!")
    
except Exception as e:
    print(f"❌ Lỗi: {e}")

# Demo 3: Access lần 2 - instant
print("\n3️⃣ ACCESS LẦN 2 (trả về instance có sẵn)")
print("-" * 30)

print("🔍 Truy cập llm_service lần 2...")
start_time = time.time()

llm_service2 = llm_module.llm_service
access_time = time.time() - start_time

print(f"⚡ Truy cập lần 2 trong {access_time:.4f} giây")

if llm_service is llm_service2:
    print("✅ Cùng instance (Singleton pattern hoạt động)")
    print("   → Không tạo instance mới!")
else:
    print("❌ Khác instance (Singleton bị lỗi)")

# Demo 4: So sánh performance
print("\n4️⃣ SO SÁNH PERFORMANCE")
print("-" * 30)

print("📊 Thời gian:")
print(f"   Import module: {import_time:.4f}s (rất nhanh)")
print(f"   Tạo instance:  {creation_time:.4f}s (chậm hơn - khởi tạo resources)")
print(f"   Access lần 2:  {access_time:.4f}s (tức thì)")

print("\n💡 KẾT LUẬN:")
print("✅ Lazy Loading hoạt động ĐÚNG!")
print("✅ Services chỉ khởi tạo KHI CẦN THIẾT")
print("✅ Startup nhanh hơn vì không khởi tạo tất cả")
print("✅ Memory tiết kiệm hơn")

print("\n🔄 Tại sao server vẫn khởi tạo services khi start?")
print("→ Vì API endpoints import services ngay từ đầu")
print("→ Cần dùng dependency injection để thực sự lazy")

print("\n🎉 Demo hoàn thành!")
