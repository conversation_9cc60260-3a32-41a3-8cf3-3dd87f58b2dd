#!/usr/bin/env python3
"""
Test script for ExamGenerationService singleton
"""

try:
    print("Testing ExamGenerationService import...")
    from app.services.exam_generation_service import ExamGenerationService
    print("✅ ExamGenerationService class import successful")
    
    print("Testing singleton pattern...")
    service1 = ExamGenerationService()
    service2 = ExamGenerationService()
    print(f"✅ Singleton test: {service1 is service2}")
    
    print("Testing service registry...")
    from app.core.service_registry import get_exam_generation_service
    service3 = get_exam_generation_service()
    print(f"✅ Service registry works: {type(service3)}")
    
    print("Testing singleton consistency...")
    print(f"✅ Registry singleton test: {service1 is service3}")
    
    print("All tests passed! ✅")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
