#!/usr/bin/env python3
"""
Script để test và verify hệ thống Singleton
Ch<PERSON> các test cases và kiểm tra tính đúng đắn của implementation
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.service_registry import (
    SERVICE_REGISTRY,
    get_service_status,
    initialize_async_services,
    cleanup_all_services,
    list_services
)

# Import all services
from app.services.llm_service import LLMService, llm_service
from app.services.simple_ocr_service import SimpleOCRService, simple_ocr_service
from app.services.docx_export_service import DocxExportService, docx_export_service
from app.services.docx_upload_service import DocxUploadService, docx_upload_service
from app.services.exam_docx_service import ExamDocxService, exam_docx_service
from app.services.lesson_plan_framework_service import Lesson<PERSON>lanFrameworkService, lesson_plan_framework_service
from app.services.kafka_service import KafkaService, kafka_service


def test_singleton_behavior():
    """Test basic singleton behavior"""
    print("🧪 Testing Singleton Behavior...")
    
    # Test 1: Multiple instantiations should return same instance
    print("  📋 Test 1: Multiple instantiations")
    llm1 = LLMService()
    llm2 = LLMService()
    assert llm1 is llm2, "❌ LLMService instances are not the same!"
    print("    ✅ LLMService: Multiple instances are the same")
    
    ocr1 = SimpleOCRService()
    ocr2 = SimpleOCRService()
    assert ocr1 is ocr2, "❌ SimpleOCRService instances are not the same!"
    print("    ✅ SimpleOCRService: Multiple instances are the same")
    
    # Test 2: Global instances should be singletons
    print("  📋 Test 2: Global instances are singletons")
    assert llm_service is LLMService(), "❌ Global llm_service is not singleton!"
    print("    ✅ llm_service is singleton")
    
    assert simple_ocr_service is SimpleOCRService(), "❌ Global simple_ocr_service is not singleton!"
    print("    ✅ simple_ocr_service is singleton")
    
    assert docx_export_service is DocxExportService(), "❌ Global docx_export_service is not singleton!"
    print("    ✅ docx_export_service is singleton")
    
    print("✅ All singleton behavior tests passed!")


def test_service_registry():
    """Test service registry functionality"""
    print("\n🗂️ Testing Service Registry...")
    
    # Test 1: List all services
    services = list_services()
    print(f"  📋 Registered services ({len(services)}):")
    for service_name in services:
        print(f"    - {service_name}")
    
    # Test 2: Get service status
    print("  📋 Service Status:")
    status = get_service_status()
    for service_name, info in status.items():
        status_icon = "✅" if info.get('initialized', False) else "❌"
        print(f"    {status_icon} {service_name}: {info.get('class', 'Unknown')}")
    
    print("✅ Service registry tests completed!")


def test_lazy_initialization():
    """Test lazy initialization"""
    print("\n⏳ Testing Lazy Initialization...")
    
    # Test that services are initialized only when first accessed
    services_to_test = [
        ('LLMService', LLMService),
        ('SimpleOCRService', SimpleOCRService),
        ('DocxExportService', DocxExportService),
    ]
    
    for service_name, service_class in services_to_test:
        print(f"  📋 Testing {service_name}...")
        
        # Create instance (should trigger initialization)
        start_time = time.time()
        instance = service_class()
        init_time = time.time() - start_time
        
        # Create second instance (should be instant)
        start_time = time.time()
        instance2 = service_class()
        second_time = time.time() - start_time
        
        assert instance is instance2, f"❌ {service_name} instances are not the same!"
        print(f"    ✅ {service_name}: First init: {init_time:.4f}s, Second: {second_time:.4f}s")
    
    print("✅ Lazy initialization tests passed!")


async def test_async_services():
    """Test async service functionality"""
    print("\n🔄 Testing Async Services...")
    
    try:
        print("  📋 Initializing async services...")
        await initialize_async_services()
        print("    ✅ Async services initialized successfully")
    except Exception as e:
        print(f"    ⚠️ Some async services failed (expected in test environment): {e}")
    
    try:
        print("  📋 Testing service cleanup...")
        await cleanup_all_services()
        print("    ✅ Service cleanup completed successfully")
    except Exception as e:
        print(f"    ⚠️ Some cleanup failed (expected in test environment): {e}")
    
    print("✅ Async service tests completed!")


def test_memory_usage():
    """Test memory usage of singleton pattern"""
    print("\n💾 Testing Memory Usage...")
    
    import gc
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"  📋 Initial memory usage: {initial_memory:.2f} MB")
    
    # Create many instances (should not increase memory significantly)
    instances = []
    for i in range(100):
        instances.extend([
            LLMService(),
            SimpleOCRService(),
            DocxExportService(),
            DocxUploadService(),
            ExamDocxService(),
        ])
    
    gc.collect()  # Force garbage collection
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_increase = final_memory - initial_memory
    
    print(f"  📋 Final memory usage: {final_memory:.2f} MB")
    print(f"  📋 Memory increase: {memory_increase:.2f} MB")
    
    # Memory increase should be minimal (less than 10MB for 500 instances)
    if memory_increase < 10:
        print("    ✅ Memory usage is efficient (singleton pattern working)")
    else:
        print("    ⚠️ Memory usage might be higher than expected")
    
    print("✅ Memory usage test completed!")


def performance_benchmark():
    """Benchmark singleton vs regular instantiation"""
    print("\n⚡ Performance Benchmark...")
    
    import timeit
    
    # Benchmark singleton instantiation
    singleton_time = timeit.timeit(
        lambda: LLMService(),
        number=10000
    )
    
    print(f"  📋 10,000 singleton instantiations: {singleton_time:.4f} seconds")
    print(f"  📋 Average per instantiation: {singleton_time/10000*1000:.4f} ms")
    
    # Test that all instances are the same
    instances = [LLMService() for _ in range(1000)]
    all_same = all(instance is instances[0] for instance in instances)
    
    if all_same:
        print("    ✅ All 1,000 instances are the same object")
    else:
        print("    ❌ Some instances are different objects!")
    
    print("✅ Performance benchmark completed!")


async def main():
    """Run all tests"""
    print("🚀 Starting Singleton System Tests...")
    print("=" * 50)
    
    try:
        # Run all tests
        test_singleton_behavior()
        test_service_registry()
        test_lazy_initialization()
        await test_async_services()
        test_memory_usage()
        performance_benchmark()
        
        print("\n" + "=" * 50)
        print("🎉 All tests completed successfully!")
        print("✅ Singleton pattern is working correctly")
        print("✅ Service registry is functioning properly")
        print("✅ Lazy initialization is working as expected")
        print("✅ Memory usage is efficient")
        print("✅ Performance is optimal")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    # Run the test suite
    asyncio.run(main())
