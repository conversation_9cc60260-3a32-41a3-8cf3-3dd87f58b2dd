#!/usr/bin/env python3
"""
Simple test để kiểm tra lazy initialization
"""

import sys
import os
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("🧪 Simple Lazy Initialization Test")
print("=" * 40)

print("\n1️⃣ Importing modules (should be fast)...")
start_time = time.time()

try:
    # Import module - should NOT create instance
    print("📦 Importing llm_service module...")
    import app.services.llm_service as llm_module
    
    import_time = time.time() - start_time
    print(f"✅ Import completed in {import_time:.3f} seconds")
    
    print("\n2️⃣ Accessing service (should create instance now)...")
    start_time = time.time()
    
    # Access service - should create instance NOW
    print("🔍 Accessing llm_service...")
    llm_service = llm_module.llm_service
    
    access_time = time.time() - start_time
    print(f"✅ Service created in {access_time:.3f} seconds")
    print(f"📋 Service type: {type(llm_service).__name__}")
    
    print("\n3️⃣ Accessing again (should be instant)...")
    start_time = time.time()
    
    llm_service2 = llm_module.llm_service
    access_time2 = time.time() - start_time
    
    print(f"⏱️ Second access: {access_time2:.3f} seconds")
    
    if llm_service is llm_service2:
        print("✅ Same instance returned (singleton working)")
    else:
        print("❌ Different instances (singleton broken)")
    
    print("\n🎉 Test completed successfully!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
