#!/usr/bin/env python3
"""
Test script để verify True Lazy Initialization
Kiểm tra xem services có thực sự chỉ khởi tạo khi cần thiết không
"""

import sys
import os
import time
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_lazy_initialization():
    """Test true lazy initialization"""
    
    print("🧪 Testing True Lazy Initialization")
    print("=" * 50)
    
    # Test 1: Import modules without accessing services
    print("\n1️⃣ Testing module imports (should NOT create instances)")
    print("-" * 40)
    
    start_time = time.time()
    
    # Import modules - should not create instances
    print("📦 Importing service modules...")
    
    try:
        # These imports should NOT trigger service creation
        import app.services.llm_service as llm_module
        print("✅ llm_service module imported (no instance created yet)")
        
        import app.services.simple_ocr_service as ocr_module  
        print("✅ simple_ocr_service module imported (no instance created yet)")
        
        # Import service registry - should not create instances
        import app.core.service_registry as registry_module
        print("✅ service_registry module imported (no instances created yet)")
        
        import_time = time.time() - start_time
        print(f"⏱️ Import time: {import_time:.3f} seconds")
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Test 2: Access services - should create instances now
    print("\n2️⃣ Testing service access (should create instances NOW)")
    print("-" * 40)
    
    # Access LLM service - should create instance now
    print("🔍 Accessing llm_service...")
    start_time = time.time()
    
    try:
        llm_service = llm_module.llm_service
        access_time = time.time() - start_time
        print(f"✅ LLM Service accessed and created: {type(llm_service).__name__}")
        print(f"⏱️ Creation time: {access_time:.3f} seconds")
        
        # Access again - should return same instance
        start_time = time.time()
        llm_service2 = llm_module.llm_service
        access_time2 = time.time() - start_time
        
        if llm_service is llm_service2:
            print(f"✅ Same instance returned (singleton working)")
            print(f"⏱️ Second access time: {access_time2:.3f} seconds (should be much faster)")
        else:
            print("❌ Different instances returned (singleton not working)")
            return False
            
    except Exception as e:
        print(f"❌ LLM Service access failed: {e}")
        return False
    
    # Test 3: Access OCR service
    print("\n🔍 Accessing simple_ocr_service...")
    start_time = time.time()
    
    try:
        ocr_service = ocr_module.simple_ocr_service
        access_time = time.time() - start_time
        print(f"✅ OCR Service accessed and created: {type(ocr_service).__name__}")
        print(f"⏱️ Creation time: {access_time:.3f} seconds")
        
    except Exception as e:
        print(f"❌ OCR Service access failed: {e}")
        return False
    
    # Test 4: Service Registry lazy loading
    print("\n3️⃣ Testing Service Registry lazy loading")
    print("-" * 40)
    
    try:
        print("🔍 Accessing service via registry...")
        start_time = time.time()
        
        # This should trigger lazy loading in registry
        registry_llm = registry_module.get_llm_service()
        access_time = time.time() - start_time
        
        print(f"✅ Service from registry: {type(registry_llm).__name__}")
        print(f"⏱️ Registry access time: {access_time:.3f} seconds")
        
        # Should be same instance
        if llm_service is registry_llm:
            print("✅ Registry returns same singleton instance")
        else:
            print("❌ Registry returns different instance")
            return False
            
    except Exception as e:
        print(f"❌ Service registry test failed: {e}")
        return False
    
    # Test 5: Memory usage comparison
    print("\n4️⃣ Memory Usage Analysis")
    print("-" * 40)
    
    try:
        import psutil
        import gc
        
        # Force garbage collection
        gc.collect()
        
        process = psutil.Process()
        memory_info = process.memory_info()
        
        print(f"📊 Current memory usage:")
        print(f"   RSS: {memory_info.rss / 1024 / 1024:.2f} MB")
        print(f"   VMS: {memory_info.vms / 1024 / 1024:.2f} MB")
        
    except ImportError:
        print("⚠️ psutil not available for memory analysis")
    except Exception as e:
        print(f"⚠️ Memory analysis failed: {e}")
    
    print("\n🎉 All tests passed! True Lazy Initialization is working correctly!")
    return True

def test_startup_behavior():
    """Test startup behavior without accessing services"""
    
    print("\n🚀 Testing Startup Behavior")
    print("=" * 50)
    
    print("📝 This test shows that services are NOT created during import")
    print("   Only when you actually access them!")
    
    # Show what happens during normal import
    print("\n✨ Key Benefits of True Lazy Initialization:")
    print("   1. Faster application startup")
    print("   2. Lower memory usage until services are needed")
    print("   3. Services only initialize when first accessed")
    print("   4. Thread-safe singleton pattern")
    print("   5. Automatic resource management")

if __name__ == "__main__":
    print("🔬 True Lazy Initialization Test Suite")
    print("=" * 60)
    
    try:
        # Test lazy initialization
        success = test_lazy_initialization()
        
        if success:
            test_startup_behavior()
            print("\n🎯 Conclusion: True Lazy Initialization implemented successfully!")
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
