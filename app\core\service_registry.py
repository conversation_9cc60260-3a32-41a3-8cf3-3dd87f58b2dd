"""
Service Registry - <PERSON><PERSON><PERSON><PERSON> lý tất cả singleton services
Cung cấp factory functions và centralized access
"""

import logging
from typing import Dict, Any, List
from app.core.singleton_base import create_singleton_factory

logger = logging.getLogger(__name__)

# Service registry dictionary - lazy import để tránh circular imports
SERVICE_REGISTRY: Dict[str, Any] = {}

def _get_service_class(service_name: str):
    """Get service class without creating instance"""
    service_classes = {
        'llm_service': ('app.services.llm_service', 'LLMService'),
        'lesson_plan_framework_service': ('app.services.lesson_plan_framework_service', 'LessonPlanFrameworkService'),
        'kafka_service': ('app.services.kafka_service', 'KafkaService'),
        'simple_ocr_service': ('app.services.simple_ocr_service', 'SimpleOCRService'),
        'docx_export_service': ('app.services.docx_export_service', 'DocxExportService'),
        'docx_upload_service': ('app.services.docx_upload_service', 'DocxUploadService'),
        'exam_docx_service': ('app.services.exam_docx_service', 'ExamDocxService'),
    }

    if service_name not in service_classes:
        raise KeyError(f"Unknown service: {service_name}")

    module_name, class_name = service_classes[service_name]

    # Dynamic import without creating instance
    import importlib
    module = importlib.import_module(module_name)
    service_class = getattr(module, class_name)

    return service_class

def _initialize_service_registry():
    """Initialize service registry with true lazy loading"""
    if not SERVICE_REGISTRY:
        try:
            # Create instances only when registry is accessed
            for service_name in ['llm_service', 'lesson_plan_framework_service', 'kafka_service',
                               'simple_ocr_service', 'docx_export_service', 'docx_upload_service', 'exam_docx_service']:
                if service_name not in SERVICE_REGISTRY:
                    service_class = _get_service_class(service_name)
                    # Create singleton instance - this will trigger lazy initialization
                    SERVICE_REGISTRY[service_name] = service_class()
                    logger.info(f"✅ {service_name} added to registry (lazy)")

            logger.info(f"✅ Service registry initialized with {len(SERVICE_REGISTRY)} services")
        except Exception as e:
            logger.error(f"❌ Failed to initialize service registry: {e}")
            raise

# Async services that need async initialization
ASYNC_SERVICES = [
    'lesson_plan_framework_service',
    'kafka_service',
]

# Factory functions - lazy initialization
def get_llm_service():
    """Get LLM service singleton instance"""
    _initialize_service_registry()
    return SERVICE_REGISTRY['llm_service']

def get_lesson_plan_framework_service():
    """Get Lesson Plan Framework service singleton instance"""
    _initialize_service_registry()
    return SERVICE_REGISTRY['lesson_plan_framework_service']

def get_kafka_service():
    """Get Kafka service singleton instance"""
    _initialize_service_registry()
    return SERVICE_REGISTRY['kafka_service']

def get_simple_ocr_service():
    """Get Simple OCR service singleton instance"""
    _initialize_service_registry()
    return SERVICE_REGISTRY['simple_ocr_service']

def get_docx_export_service():
    """Get DOCX Export service singleton instance"""
    _initialize_service_registry()
    return SERVICE_REGISTRY['docx_export_service']

def get_docx_upload_service():
    """Get DOCX Upload service singleton instance"""
    _initialize_service_registry()
    return SERVICE_REGISTRY['docx_upload_service']

def get_exam_docx_service():
    """Get Exam DOCX service singleton instance"""
    _initialize_service_registry()
    return SERVICE_REGISTRY['exam_docx_service']


def get_service(service_name: str) -> Any:
    """
    Get service by name from registry

    Args:
        service_name: Name of the service

    Returns:
        Service instance

    Raises:
        KeyError: If service not found
    """
    _initialize_service_registry()
    if service_name not in SERVICE_REGISTRY:
        raise KeyError(f"Service '{service_name}' not found in registry")

    return SERVICE_REGISTRY[service_name]


def list_services() -> List[str]:
    """
    Get list of all registered service names

    Returns:
        List of service names
    """
    _initialize_service_registry()
    return list(SERVICE_REGISTRY.keys())


async def initialize_async_services():
    """
    Initialize all async services
    """
    _initialize_service_registry()
    logger.info("🚀 Initializing async services...")

    for service_name in ASYNC_SERVICES:
        try:
            service = SERVICE_REGISTRY[service_name]
            if hasattr(service, 'initialize_async'):
                await service.initialize_async()
                logger.info(f"✅ {service_name} async initialized")
            else:
                logger.warning(f"⚠️ {service_name} doesn't have initialize_async method")
        except Exception as e:
            logger.error(f"❌ Failed to initialize {service_name}: {e}")


async def cleanup_all_services():
    """
    Cleanup all services
    """
    _initialize_service_registry()
    logger.info("🧹 Cleaning up all services...")

    for service_name, service in SERVICE_REGISTRY.items():
        try:
            # Try async cleanup first
            if hasattr(service, 'close_async'):
                await service.close_async()
                logger.info(f"✅ {service_name} async cleanup completed")
            # Then try sync cleanup
            elif hasattr(service, 'close'):
                service.close()
                logger.info(f"✅ {service_name} sync cleanup completed")
            else:
                logger.info(f"ℹ️ {service_name} doesn't need cleanup")
        except Exception as e:
            logger.error(f"❌ Error cleaning up {service_name}: {e}")


def get_service_status() -> Dict[str, Dict[str, Any]]:
    """
    Get status of all services

    Returns:
        Dict with service status information
    """
    _initialize_service_registry()
    status = {}

    for service_name, service in SERVICE_REGISTRY.items():
        try:
            service_status = {
                'name': service_name,
                'class': service.__class__.__name__,
                'initialized': True,
                'is_async': service_name in ASYNC_SERVICES,
                'has_cleanup': hasattr(service, 'close') or hasattr(service, 'close_async'),
            }
            
            # Check if service has specific status methods
            if hasattr(service, 'is_available'):
                service_status['available'] = service.is_available()
            
            if hasattr(service, 'is_connected'):
                service_status['connected'] = service.is_connected
                
            status[service_name] = service_status
            
        except Exception as e:
            status[service_name] = {
                'name': service_name,
                'error': str(e),
                'initialized': False
            }
    
    return status


# Lazy getters for direct service access
def __getattr__(name: str):
    """Lazy loading for service instances"""
    service_map = {
        'llm_service': 'llm_service',
        'lesson_plan_framework_service': 'lesson_plan_framework_service',
        'kafka_service': 'kafka_service',
        'simple_ocr_service': 'simple_ocr_service',
        'docx_export_service': 'docx_export_service',
        'docx_upload_service': 'docx_upload_service',
        'exam_docx_service': 'exam_docx_service',
    }

    if name in service_map:
        _initialize_service_registry()
        return SERVICE_REGISTRY[service_map[name]]

    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


# Export commonly used services for easy import
__all__ = [
    'SERVICE_REGISTRY',
    'ASYNC_SERVICES',
    'get_service',
    'list_services',
    'initialize_async_services',
    'cleanup_all_services',
    'get_service_status',
    # Factory functions
    'get_llm_service',
    'get_lesson_plan_framework_service',
    'get_kafka_service',
    'get_simple_ocr_service',
    'get_docx_export_service',
    'get_docx_upload_service',
    'get_exam_docx_service',
]
