"""
Service Registry - <PERSON><PERSON><PERSON><PERSON> lý tất cả singleton services
Cung cấp factory functions và centralized access
"""

import logging
from typing import Dict, Any, List
from app.core.singleton_base import create_singleton_factory

logger = logging.getLogger(__name__)

# Import all singleton services
from app.services.llm_service import llm_service
from app.services.lesson_plan_framework_service import lesson_plan_framework_service
from app.services.kafka_service import kafka_service
from app.services.simple_ocr_service import simple_ocr_service
from app.services.docx_export_service import docx_export_service
from app.services.docx_upload_service import docx_upload_service
from app.services.exam_docx_service import exam_docx_service

# Service registry dictionary
SERVICE_REGISTRY: Dict[str, Any] = {
    'llm_service': llm_service,
    'lesson_plan_framework_service': lesson_plan_framework_service,
    'kafka_service': kafka_service,
    'simple_ocr_service': simple_ocr_service,
    'docx_export_service': docx_export_service,
    'docx_upload_service': docx_upload_service,
    'exam_docx_service': exam_docx_service,
}

# Async services that need async initialization
ASYNC_SERVICES = [
    'lesson_plan_framework_service',
    'kafka_service',
]

# Factory functions
get_llm_service = create_singleton_factory(llm_service.__class__)
get_lesson_plan_framework_service = create_singleton_factory(lesson_plan_framework_service.__class__)
get_kafka_service = create_singleton_factory(kafka_service.__class__)
get_simple_ocr_service = create_singleton_factory(simple_ocr_service.__class__)
get_docx_export_service = create_singleton_factory(docx_export_service.__class__)
get_docx_upload_service = create_singleton_factory(docx_upload_service.__class__)
get_exam_docx_service = create_singleton_factory(exam_docx_service.__class__)


def get_service(service_name: str) -> Any:
    """
    Get service by name from registry
    
    Args:
        service_name: Name of the service
        
    Returns:
        Service instance
        
    Raises:
        KeyError: If service not found
    """
    if service_name not in SERVICE_REGISTRY:
        raise KeyError(f"Service '{service_name}' not found in registry")
    
    return SERVICE_REGISTRY[service_name]


def list_services() -> List[str]:
    """
    Get list of all registered service names
    
    Returns:
        List of service names
    """
    return list(SERVICE_REGISTRY.keys())


async def initialize_async_services():
    """
    Initialize all async services
    """
    logger.info("🚀 Initializing async services...")
    
    for service_name in ASYNC_SERVICES:
        try:
            service = SERVICE_REGISTRY[service_name]
            if hasattr(service, 'initialize_async'):
                await service.initialize_async()
                logger.info(f"✅ {service_name} async initialized")
            else:
                logger.warning(f"⚠️ {service_name} doesn't have initialize_async method")
        except Exception as e:
            logger.error(f"❌ Failed to initialize {service_name}: {e}")


async def cleanup_all_services():
    """
    Cleanup all services
    """
    logger.info("🧹 Cleaning up all services...")
    
    for service_name, service in SERVICE_REGISTRY.items():
        try:
            # Try async cleanup first
            if hasattr(service, 'close_async'):
                await service.close_async()
                logger.info(f"✅ {service_name} async cleanup completed")
            # Then try sync cleanup
            elif hasattr(service, 'close'):
                service.close()
                logger.info(f"✅ {service_name} sync cleanup completed")
            else:
                logger.info(f"ℹ️ {service_name} doesn't need cleanup")
        except Exception as e:
            logger.error(f"❌ Error cleaning up {service_name}: {e}")


def get_service_status() -> Dict[str, Dict[str, Any]]:
    """
    Get status of all services
    
    Returns:
        Dict with service status information
    """
    status = {}
    
    for service_name, service in SERVICE_REGISTRY.items():
        try:
            service_status = {
                'name': service_name,
                'class': service.__class__.__name__,
                'initialized': True,
                'is_async': service_name in ASYNC_SERVICES,
                'has_cleanup': hasattr(service, 'close') or hasattr(service, 'close_async'),
            }
            
            # Check if service has specific status methods
            if hasattr(service, 'is_available'):
                service_status['available'] = service.is_available()
            
            if hasattr(service, 'is_connected'):
                service_status['connected'] = service.is_connected
                
            status[service_name] = service_status
            
        except Exception as e:
            status[service_name] = {
                'name': service_name,
                'error': str(e),
                'initialized': False
            }
    
    return status


# Export commonly used services for easy import
__all__ = [
    'SERVICE_REGISTRY',
    'ASYNC_SERVICES',
    'get_service',
    'list_services',
    'initialize_async_services',
    'cleanup_all_services',
    'get_service_status',
    # Factory functions
    'get_llm_service',
    'get_lesson_plan_framework_service',
    'get_kafka_service',
    'get_simple_ocr_service',
    'get_docx_export_service',
    'get_docx_upload_service',
    'get_exam_docx_service',
    # Direct service instances
    'llm_service',
    'lesson_plan_framework_service',
    'kafka_service',
    'simple_ocr_service',
    'docx_export_service',
    'docx_upload_service',
    'exam_docx_service',
]
