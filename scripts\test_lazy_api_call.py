#!/usr/bin/env python3
"""
Test script để kiểm tra lazy loading khi gọi API endpoints
"""

import requests
import time

def test_api_endpoint():
    """Test gọi API endpoint để trigger lazy loading"""
    
    print("🧪 Testing Lazy Loading with API call...")
    print("📞 Calling GET /api/v1/health")

    try:
        # Gọi health endpoint trước
        response = requests.get("http://localhost:8000/api/v1/health")
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API call successful!")
            print(f"📋 Health status: {data.get('status', 'Unknown')}")
        else:
            print(f"❌ API call failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error calling API: {e}")

if __name__ == "__main__":
    print("🚀 Starting Lazy Loading Test...")
    print("⏰ Waiting 2 seconds for server to be ready...")
    time.sleep(2)
    
    test_api_endpoint()
    
    print("\n🔍 Check server logs to see if services were created lazily!")
