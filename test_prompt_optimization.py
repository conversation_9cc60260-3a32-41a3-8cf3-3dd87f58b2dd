#!/usr/bin/env python3
"""
Test tối ưu prompt template
"""

import sys
import os

# Thêm path để import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.lesson_plan_content_service import LessonPlanContentService

def test_prompt_templates():
    """Test các prompt template đã được tối ưu"""
    print("=== TESTING OPTIMIZED PROMPT TEMPLATES ===")
    
    service = LessonPlanContentService()
    
    # Test cases với các loại node khác nhau
    test_nodes = [
        {
            "id": 1,
            "title": "Mục tiêu bài học",
            "type": "PARAGRAPH",
            "parentId": None
        },
        {
            "id": 2,
            "title": "Hoạt động khởi động",
            "type": "LIST_ITEM",
            "parentId": 1
        },
        {
            "id": 3,
            "title": "Câu hỏi kiểm tra",
            "type": "PARAGRAPH",
            "parentId": 2
        },
        {
            "id": 4,
            "title": "Bài tập về nhà",
            "type": "LIST_ITEM",
            "parentId": 3
        },
        {
            "id": 5,
            "title": "Đánh giá kết quả",
            "type": "SECTION",
            "parentId": None
        }
    ]
    
    lesson_content = "Nội dung bài học mẫu về Toán học lớp 10"
    
    for i, node in enumerate(test_nodes, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {node['type']} - {node['title']}")
        print(f"{'='*60}")
        
        # Tạo prompt
        prompt = service._create_content_generation_prompt(node, lesson_content)
        
        # Hiển thị prompt
        print("GENERATED PROMPT:")
        print("-" * 40)
        print(prompt)
        print("-" * 40)
        
        # Kiểm tra các thành phần quan trọng
        checks = {
            "Có role giáo viên": "giáo viên" in prompt.lower(),
            "Có thông tin node": f"ID: {node['id']}" in prompt,
            "Có tiêu đề": node['title'] in prompt,
            "Có loại node": node['type'] in prompt,
            "Có yêu cầu cơ bản": "YÊU CẦU:" in prompt,
            "Có định dạng đầu ra": "ĐỊNH DẠNG ĐẦU RA:" in prompt,
        }
        
        # Kiểm tra yêu cầu đặc biệt dựa trên tiêu đề
        title_lower = node['title'].lower()
        if "mục tiêu" in title_lower:
            checks["Có yêu cầu mục tiêu"] = "mục tiêu kiến thức" in prompt.lower()
        elif "hoạt động" in title_lower:
            checks["Có yêu cầu hoạt động"] = "các bước thực hiện" in prompt.lower()
        elif "câu hỏi" in title_lower:
            checks["Có yêu cầu câu hỏi"] = "phù hợp với trình độ" in prompt.lower()
        elif "bài tập" in title_lower:
            checks["Có yêu cầu bài tập"] = "số trang" in prompt.lower()
        elif "đánh giá" in title_lower:
            checks["Có yêu cầu đánh giá"] = "tiêu chí đánh giá" in prompt.lower()
        
        print("\nCHECKS:")
        for check_name, result in checks.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {check_name}: {status}")
        
        # Tính tỷ lệ pass
        pass_count = sum(1 for result in checks.values() if result)
        total_count = len(checks)
        pass_rate = (pass_count / total_count) * 100
        print(f"\nPASS RATE: {pass_count}/{total_count} ({pass_rate:.1f}%)")

def test_template_consistency():
    """Test tính nhất quán của template"""
    print(f"\n{'='*60}")
    print("TESTING TEMPLATE CONSISTENCY")
    print(f"{'='*60}")
    
    service = LessonPlanContentService()
    
    # Test với cùng một node type nhưng tiêu đề khác nhau
    paragraph_nodes = [
        {"id": 1, "title": "Giới thiệu bài học", "type": "PARAGRAPH"},
        {"id": 2, "title": "Mục tiêu bài học", "type": "PARAGRAPH"},
        {"id": 3, "title": "Nội dung chính", "type": "PARAGRAPH"}
    ]
    
    prompts = []
    for node in paragraph_nodes:
        prompt = service._create_content_generation_prompt(node, "Test content")
        prompts.append(prompt)
    
    # Kiểm tra các phần chung
    common_parts = [
        "Bạn là một giáo viên",
        "YÊU CẦU:",
        "ĐỊNH DẠNG ĐẦU RA:",
        "THÔNG TIN NODE:",
        "NGỮ CẢNH GIÁO ÁN:"
    ]
    
    print("CONSISTENCY CHECKS:")
    for part in common_parts:
        all_have_part = all(part in prompt for prompt in prompts)
        status = "✅ CONSISTENT" if all_have_part else "❌ INCONSISTENT"
        print(f"  {part}: {status}")
    
    # Kiểm tra độ dài prompt
    lengths = [len(prompt) for prompt in prompts]
    min_len, max_len = min(lengths), max(lengths)
    length_variance = max_len - min_len
    
    print(f"\nPROMPT LENGTHS:")
    print(f"  Min: {min_len} chars")
    print(f"  Max: {max_len} chars") 
    print(f"  Variance: {length_variance} chars")
    print(f"  Status: {'✅ REASONABLE' if length_variance < 500 else '❌ TOO VARIABLE'}")

if __name__ == "__main__":
    test_prompt_templates()
    test_template_consistency()
    print(f"\n{'='*60}")
    print("PROMPT OPTIMIZATION TEST COMPLETED")
    print(f"{'='*60}")
