"""
Base Singleton class để áp dụng cho tất cả services
Global Singleton + Lazy Initialization pattern
"""

import threading
import logging
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class SingletonMeta(type):
    """
    Metaclass để implement Singleton pattern
    Thread-safe với double-checked locking
    """
    
    _instances: Dict[type, Any] = {}
    _locks: Dict[type, threading.Lock] = {}
    _initialized: Dict[type, bool] = {}
    
    def __call__(cls, *args, **kwargs):
        """
        Thread-safe singleton implementation
        """
        # Tạo lock cho class nếu chưa có
        if cls not in cls._locks:
            cls._locks[cls] = threading.Lock()
        
        # Double-checked locking pattern
        if cls not in cls._instances:
            with cls._locks[cls]:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
                    cls._initialized[cls] = False
        
        return cls._instances[cls]
    
    @classmethod
    def reset_instance(mcs, cls):
        """Reset singleton instance (for testing)"""
        if cls in mcs._instances:
            with mcs._locks.get(cls, threading.Lock()):
                if cls in mcs._instances:
                    del mcs._instances[cls]
                if cls in mcs._initialized:
                    del mcs._initialized[cls]
                logger.info(f"🔄 {cls.__name__} singleton instance reset")


class SingletonService(metaclass=SingletonMeta):
    """
    Base class cho tất cả services sử dụng Singleton pattern
    Với lazy initialization và proper cleanup
    """
    
    def __init__(self):
        """
        Base initialization - chỉ chạy 1 lần duy nhất
        Subclasses nên override _initialize() method
        """
        cls = self.__class__
        
        # Lazy initialization - chỉ init 1 lần
        if not SingletonMeta._initialized.get(cls, False):
            with SingletonMeta._locks.get(cls, threading.Lock()):
                if not SingletonMeta._initialized.get(cls, False):
                    logger.info(f"🚀 Initializing {cls.__name__}...")
                    self._initialize()
                    SingletonMeta._initialized[cls] = True
                    logger.info(f"✅ {cls.__name__} initialized successfully")
    
    def _initialize(self):
        """
        Override this method in subclasses for actual initialization
        Được gọi chỉ 1 lần duy nhất khi service được tạo
        """
        pass
    
    def close(self):
        """
        Cleanup resources - override in subclasses if needed
        """
        try:
            logger.info(f"🧹 Cleaning up {self.__class__.__name__} resources...")
            self._cleanup()
            logger.info(f"✅ {self.__class__.__name__} cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during {self.__class__.__name__} cleanup: {e}")
    
    def _cleanup(self):
        """
        Override this method in subclasses for actual cleanup
        """
        pass
    
    @classmethod
    def reset_instance(cls):
        """
        Reset singleton instance (chỉ dùng cho testing)
        """
        SingletonMeta.reset_instance(cls)
    
    @classmethod
    def get_instance(cls):
        """
        Factory method để lấy singleton instance
        """
        return cls()


class AsyncSingletonService(SingletonService):
    """
    Base class cho async services sử dụng Singleton pattern
    """
    
    def __init__(self):
        """
        Async service initialization
        """
        super().__init__()
    
    async def initialize_async(self):
        """
        Async initialization method - override in subclasses
        Được gọi từ startup event handler
        """
        cls = self.__class__
        
        if not hasattr(self, '_async_initialized'):
            logger.info(f"🚀 Async initializing {cls.__name__}...")
            await self._initialize_async()
            self._async_initialized = True
            logger.info(f"✅ {cls.__name__} async initialized successfully")
    
    async def _initialize_async(self):
        """
        Override this method in subclasses for async initialization
        """
        pass
    
    async def close_async(self):
        """
        Async cleanup resources
        """
        try:
            logger.info(f"🧹 Async cleaning up {self.__class__.__name__} resources...")
            await self._cleanup_async()
            logger.info(f"✅ {self.__class__.__name__} async cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during {self.__class__.__name__} async cleanup: {e}")
    
    async def _cleanup_async(self):
        """
        Override this method in subclasses for async cleanup
        """
        pass


# Utility functions
def create_singleton_factory(service_class):
    """
    Factory function generator để tạo factory cho singleton services
    
    Usage:
        get_my_service = create_singleton_factory(MyService)
        service = get_my_service()
    """
    def factory():
        return service_class()
    
    factory.__name__ = f"get_{service_class.__name__.lower()}"
    factory.__doc__ = f"Factory function để lấy {service_class.__name__} singleton instance"
    
    return factory


def register_singleton_cleanup(app, *services):
    """
    Helper function để register cleanup cho multiple services
    
    Usage:
        register_singleton_cleanup(app, service1, service2, service3)
    """
    @app.on_event("shutdown")
    async def cleanup_singletons():
        """Cleanup all registered singleton services"""
        for service in services:
            try:
                if hasattr(service, 'close_async'):
                    await service.close_async()
                elif hasattr(service, 'close'):
                    service.close()
            except Exception as e:
                logger.error(f"Error cleaning up {service.__class__.__name__}: {e}")
