"""
Singleton Base Classes - Thread-safe Singleton implementation
Cung cấp base classes cho Singleton pattern với lazy initialization
"""

import threading
import logging
from typing import Dict, Type, Any, Callable
import asyncio

logger = logging.getLogger(__name__)


class SingletonMeta(type):
    """
    Thread-safe Singleton metaclass
    Sử dụng double-checked locking pattern để đảm bảo thread safety
    """
    _instances: Dict[Type, Any] = {}
    _lock: threading.Lock = threading.Lock()
    _initialized: Dict[Type, bool] = {}

    def __call__(cls, *args, **kwargs):
        # First check without lock (performance optimization)
        if cls not in cls._instances:
            # Double-checked locking pattern
            with cls._lock:
                # Check again inside the lock
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
                    
                    # Initialize the instance if not already done
                    if not cls._initialized.get(cls, False):
                        if hasattr(instance, '_initialize'):
                            try:
                                instance._initialize()
                                cls._initialized[cls] = True
                                logger.debug(f"✅ {cls.__name__} initialized successfully")
                            except Exception as e:
                                logger.error(f"❌ Failed to initialize {cls.__name__}: {e}")
                                # Remove failed instance
                                del cls._instances[cls]
                                raise
                        else:
                            cls._initialized[cls] = True
        
        return cls._instances[cls]

    def reset_instance(cls):
        """Reset singleton instance (useful for testing)"""
        with cls._lock:
            if cls in cls._instances:
                instance = cls._instances[cls]
                # Try to cleanup before removing
                if hasattr(instance, '_cleanup'):
                    try:
                        instance._cleanup()
                    except Exception as e:
                        logger.error(f"Error during cleanup of {cls.__name__}: {e}")
                
                del cls._instances[cls]
                cls._initialized[cls] = False
                logger.debug(f"🔄 {cls.__name__} instance reset")


class SingletonService(metaclass=SingletonMeta):
    """
    Base class cho synchronous singleton services
    Cung cấp lazy initialization và cleanup functionality
    """
    
    def __init__(self):
        """
        Constructor - không nên override trực tiếp
        Sử dụng _initialize() method thay thế
        """
        pass
    
    def _initialize(self):
        """
        Override method này để thực hiện initialization logic
        Method này sẽ được gọi tự động khi instance được tạo lần đầu
        """
        pass
    
    def _cleanup(self):
        """
        Override method này để thực hiện cleanup logic
        Method này sẽ được gọi khi service bị reset hoặc shutdown
        """
        pass
    
    def close(self):
        """Public method để cleanup resources"""
        try:
            self._cleanup()
            logger.info(f"✅ {self.__class__.__name__} closed successfully")
        except Exception as e:
            logger.error(f"❌ Error closing {self.__class__.__name__}: {e}")


class AsyncSingletonService(SingletonService):
    """
    Base class cho asynchronous singleton services
    Hỗ trợ async initialization và cleanup
    """
    
    def __init__(self):
        super().__init__()
        self._async_initialized = False
        self._async_lock = asyncio.Lock()
    
    def _initialize(self):
        """
        Synchronous initialization - override nếu cần
        Cho các setup không cần async (như config, basic setup)
        """
        pass
    
    async def _initialize_async(self):
        """
        Override method này cho async initialization logic
        (như database connections, network setup, etc.)
        """
        pass
    
    async def initialize_async(self):
        """
        Public method để thực hiện async initialization
        Thread-safe và chỉ chạy một lần
        """
        if not self._async_initialized:
            async with self._async_lock:
                if not self._async_initialized:
                    try:
                        await self._initialize_async()
                        self._async_initialized = True
                        logger.info(f"✅ {self.__class__.__name__} async initialized")
                    except Exception as e:
                        logger.error(f"❌ Failed to async initialize {self.__class__.__name__}: {e}")
                        raise
    
    async def close_async(self):
        """
        Async cleanup method
        Override _cleanup_async() để implement custom cleanup logic
        """
        try:
            if hasattr(self, '_cleanup_async'):
                await self._cleanup_async()
            else:
                # Fallback to sync cleanup
                self._cleanup()
            
            self._async_initialized = False
            logger.info(f"✅ {self.__class__.__name__} async closed successfully")
        except Exception as e:
            logger.error(f"❌ Error async closing {self.__class__.__name__}: {e}")
    
    async def _cleanup_async(self):
        """
        Override method này cho async cleanup logic
        """
        pass


def create_singleton_factory(singleton_class: Type) -> Callable[[], Any]:
    """
    Tạo factory function cho singleton class
    
    Args:
        singleton_class: Class cần tạo factory
        
    Returns:
        Factory function trả về singleton instance
    """
    def factory() -> Any:
        return singleton_class()
    
    factory.__name__ = f"get_{singleton_class.__name__.lower()}"
    factory.__doc__ = f"Get singleton instance of {singleton_class.__name__}"
    
    return factory


def reset_all_singletons():
    """
    Reset tất cả singleton instances (useful for testing)
    """
    logger.warning("🔄 Resetting all singleton instances...")
    
    # Get all singleton classes
    singleton_classes = list(SingletonMeta._instances.keys())
    
    for cls in singleton_classes:
        try:
            cls.reset_instance()
        except Exception as e:
            logger.error(f"Error resetting {cls.__name__}: {e}")
    
    logger.info(f"✅ Reset {len(singleton_classes)} singleton instances")


def get_singleton_stats() -> Dict[str, Any]:
    """
    Lấy thống kê về các singleton instances
    
    Returns:
        Dict chứa thông tin thống kê
    """
    return {
        'total_singletons': len(SingletonMeta._instances),
        'initialized_singletons': sum(1 for initialized in SingletonMeta._initialized.values() if initialized),
        'singleton_classes': [cls.__name__ for cls in SingletonMeta._instances.keys()],
        'initialization_status': {
            cls.__name__: SingletonMeta._initialized.get(cls, False) 
            for cls in SingletonMeta._instances.keys()
        }
    }


# Export public API
__all__ = [
    'SingletonMeta',
    'SingletonService', 
    'AsyncSingletonService',
    'create_singleton_factory',
    'reset_all_singletons',
    'get_singleton_stats'
]
